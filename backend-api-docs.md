# 后端API对接文档

## 1. 流式聊天接口

### 接口地址
```
POST http://localhost:8080/api/ai/stream
```

### 请求头
```
Content-Type: application/json
Accept: text/plain
```

### 请求体格式
```json
{
  "message": "用户输入的消息内容",
  "userId": "user-01",
  "sessionId": "session-01",
  "attachments": [
    {
      "name": "文件名.txt",
      "size": 1024,
      "type": "text/plain",
      "url": "http://localhost:8080/uploads/文件名.txt"
    }
  ]
}
```

### 字段说明
- `message`: 必填，用户输入的文本消息
- `userId`: 必填，用户唯一标识
- `sessionId`: 必填，会话唯一标识
- `attachments`: 可选，附件信息数组
  - `name`: 文件名
  - `size`: 文件大小（字节）
  - `type`: 文件MIME类型
  - `url`: 文件访问URL（上传后返回）

### 响应格式
- 响应类型：`text/plain`
- 流式返回：服务器逐步返回AI回复内容
- 编码：UTF-8

### 示例响应
```
这是AI的回复内容，会逐字符流式返回...
```

## 2. 文件上传接口

### 接口地址
```
POST http://localhost:8080/api/upload/file
```

### 请求格式
- Content-Type: `multipart/form-data`
- 表单字段：`file`（文件）

### 响应格式
```json
{
  "success": true,
  "message": "上传成功",
  "data": {
    "url": "http://localhost:8080/uploads/filename.txt",
    "filename": "filename.txt",
    "size": 1024,
    "type": "text/plain"
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "上传失败：文件大小超过限制"
}
```

## 3. 后端实现要点

### 3.1 流式响应处理
- 使用 `ResponseEntity<StreamingResponseBody>` 或 `SseEmitter`
- 设置正确的响应头：`text/plain; charset=utf-8`
- 逐步写入响应流

### 3.2 附件处理
- 在聊天请求中检查 `attachments` 字段
- 通过 `url` 字段获取已上传的文件
- 根据文件类型读取文件内容：
  - 文本文件：直接读取文本内容
  - 图片文件：可进行图像分析
  - 文档文件：提取文档内容
- 在AI回复中基于文件内容提供相关帮助

### 3.3 文件存储
- 文件保存到 `./uploads/` 目录
- 生成唯一文件名避免冲突
- 返回可访问的URL路径

### 3.4 跨域配置
```java
@CrossOrigin(origins = "http://localhost:5173")
```

## 4. 前端集成说明

### 4.1 文件上传流程
1. 用户选择文件 → Attachments组件
2. 文件自动上传到服务器 → 获得文件URL
3. 文件信息存储在 `attachedFiles` 数组（包含URL）
4. 用户发送消息时，只发送上传成功的文件信息
5. 发送后清空附件列表

### 4.2 消息显示
- 用户消息：显示文本 + 附件列表
- AI消息：Markdown渲染的回复内容
- 附件显示：文件名 + 文件大小

### 4.3 数据结构
```typescript
// 消息数据结构
interface Message {
  id: string
  message: string
  status: 'local' | 'ai' | 'loading'
  isStreaming?: boolean
  attachments?: Array<{
    name: string
    size: number
    type: string
    url?: string
  }>
}
```

## 5. 测试建议

### 5.1 功能测试
1. 上传不同类型文件（文本、图片、文档）
2. 发送带附件的消息
3. 检查用户消息气泡中的附件显示
4. 验证AI回复中提及附件信息

### 5.2 边界测试
1. 大文件上传（超过限制）
2. 不支持的文件类型
3. 网络异常情况
4. 并发上传测试

## 6. 后端文件读取示例

### 6.1 Java示例代码
```java
@PostMapping("/stream")
public ResponseEntity<StreamingResponseBody> streamChat(@RequestBody ChatRequest request) {
    StreamingResponseBody stream = outputStream -> {
        try (PrintWriter writer = new PrintWriter(outputStream, true, StandardCharsets.UTF_8)) {
            // 处理附件
            if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
                for (ChatRequest.AttachmentInfo attachment : request.getAttachments()) {
                    String fileContent = readFileFromUrl(attachment.getUrl());
                    // 基于文件内容生成AI回复
                    String response = generateAIResponse(request.getMessage(), fileContent);
                    writer.write(response);
                    writer.flush();
                }
            } else {
                // 普通消息处理
                String response = generateAIResponse(request.getMessage(), null);
                writer.write(response);
                writer.flush();
            }
        }
    };

    return ResponseEntity.ok()
        .header("Content-Type", "text/plain; charset=utf-8")
        .body(stream);
}

private String readFileFromUrl(String fileUrl) {
    try {
        // 从本地文件系统读取文件
        String filePath = fileUrl.replace("http://localhost:8080", "");
        return Files.readString(Paths.get("." + filePath), StandardCharsets.UTF_8);
    } catch (Exception e) {
        return "无法读取文件内容";
    }
}
```

## 7. 注意事项

1. **文件大小限制**：建议设置合理的文件大小限制（如10MB）
2. **文件类型限制**：根据业务需求限制允许的文件类型
3. **安全考虑**：文件名过滤、病毒扫描等
4. **存储清理**：定期清理过期的上传文件
5. **错误处理**：完善的错误提示和异常处理
6. **文件访问权限**：确保后端有权限读取上传的文件
7. **编码处理**：正确处理文件编码，特别是中文文件
