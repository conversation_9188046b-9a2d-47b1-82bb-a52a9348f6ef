<template>
  <!-- 主布局容器 -->
  <div :style="styles.layout">
    <!-- 左侧菜单栏 -->
    <div :style="styles.menu">
      <!-- 🌟 Logo区域 -->
      <div :style="styles.logo">
        <img
            src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*eco6RrQhxbMAAAAAAAAAAAAADgCCAQ/original"
            draggable="false"
            alt="logo"
            :style="styles['logo-img']"
        >
        <span :style="styles['logo-span']">Ant Design X Vue</span>
      </div>

      <!-- 🌟 新建对话按钮 -->
      <Button
          type="link"
          :style="styles.addBtn"
          @click="onAddConversation"
      >
        <PlusOutlined/>
        New Conversation
      </Button>

      <!-- 🌟 对话列表管理组件 -->
      <Conversations
          :items="conversationsItems"
          :style="styles.conversations"
          :active-key="activeKey"
          @active-change="onConversationClick"
      />
    </div>

    <!-- 右侧聊天主区域 -->
    <div :style="styles.chat">
      <!-- 🌟 消息气泡列表 - 显示所有对话消息 -->
      <Bubble.List
          :items="items"
          :roles="roles"
          :style="styles.messages"
      />

      <!-- 🌟 快捷提示词组件 - 提供预设的对话选项 -->
      <Prompts
          :items="senderPromptsItems"
          @item-click="onPromptsItemClick"
      />

      <!-- 🌟 消息输入框组件 -->
      <Sender
          :value="content"
          :style="styles.sender"
          :loading="agentRequestLoading"
          @submit="onSubmit"
          @change="value => content = value"
      >
        <!-- 输入框前缀：文件附件按钮 -->
        <template #prefix>
          <Badge :dot="attachedFiles.length > 0 && !headerOpen">
            <Button
                type="text"
                @click="() => headerOpen = !headerOpen"
            >
              <template #icon>
                <PaperClipOutlined/>
              </template>
            </Button>
          </Badge>
        </template>

        <!-- 输入框头部：文件上传区域 -->
        <template #header>
          <Sender.Header
              title="Attachments"
              :open="headerOpen"
              :styles="{ content: { padding: 0 } }"
              @open-change="open => headerOpen = open"
          >
            <!-- 文件附件组件 -->
            <Attachments
                :before-upload="() => false"
                :items="attachedFiles"
                @change="handleFileChange"
            >
              <!-- 文件上传占位符 -->
              <template #placeholder="type">
                <Flex
                    v-if="type && type.type === 'inline'"
                    align="center"
                    justify="center"
                    vertical
                    gap="2"
                >
                  <Typography.Text style="font-size: 30px; line-height: 1;">
                    <CloudUploadOutlined/>
                  </Typography.Text>
                  <Typography.Title
                      :level="5"
                      style="margin: 0; font-size: 14px; line-height: 1.5;"
                  >
                    Upload files
                  </Typography.Title>
                  <Typography.Text type="secondary">
                    Click or drag files to this area to upload
                  </Typography.Text>
                </Flex>
                <Typography.Text v-if="type && type.type === 'drop'">
                  Drop file here
                </Typography.Text>
              </template>
            </Attachments>
          </Sender.Header>
        </template>
      </Sender>
    </div>
  </div>
</template>

<script setup lang="ts">
// 导入类型定义
import {
  type AttachmentsProps,
  type BubbleListProps,
  type ConversationsProps,
  type PromptsProps
} from 'ant-design-x-vue'
import type {VNode} from 'vue'

// 导入图标组件
import {
  CloudUploadOutlined,
  CommentOutlined,
  DownloadOutlined,
  EllipsisOutlined,
  FileOutlined,
  FireOutlined,
  HeartOutlined,
  PaperClipOutlined,
  PlusOutlined,
  ReadOutlined,
  ShareAltOutlined,
  SmileOutlined,
  UserOutlined,
} from '@ant-design/icons-vue'

// 导入Ant Design Vue组件
import {Badge, Button, Flex, Space, Typography, theme, type UploadChangeParam} from 'ant-design-vue'

// 导入Ant Design X Vue AI组件
import {
  Attachments,
  Bubble,
  Conversations,
  Prompts,
  Sender,
  Welcome,
} from 'ant-design-x-vue'

// 导入Vue组合式API
import {computed, h, ref, watch} from 'vue'

// 导入markdown-it
import markdownit from 'markdown-it'
import {updateFileList} from "ant-design-vue/es/upload/utils";

// 获取主题token，用于样式计算
const {token} = theme.useToken()

// ==================== Markdown配置 ====================
// 初始化markdown-it实例，支持HTML和换行
const md = markdownit({
  html: true,
  breaks: true,
  // 可选：添加代码高亮等插件
  // highlight: function (str, lang) {
  //   if (lang && hljs.getLanguage(lang)) {
  //     try {
  //       return hljs.highlight(str, { language: lang }).value;
  //     } catch (__) {}
  //   }
  //   return ''; // 使用默认的转义
  // }
})

// ==================== Markdown渲染函数 ====================
// 自定义消息渲染函数，支持Markdown格式
const renderMarkdown = (content: string) => {
  // 检查内容是否包含Markdown语法
  const hasMarkdown = /[*_`#\[\]()>|]/.test(content)

  if (hasMarkdown) {
    return h(Typography, null, {
      default: () => h('div', {
        innerHTML: md.render(content),
        style: {
          // Markdown样式优化
          'line-height': '1.6',
          'word-wrap': 'break-word',
        }
      }),
    })
  }
  // 没有Markdown语法时直接返回文本内容
  return content
}

// ==================== 样式定义 ====================
// 使用computed计算样式，确保主题变化时样式自动更新
const styles = computed(() => {
  return {
    // 主布局样式
    'layout': {
      'width': '100%',
      'min-width': '970px',
      'height': '722px',
      'border-radius': `${token.value.borderRadius}px`,
      'display': 'flex',
      'background': `${token.value.colorBgContainer}`,
      'font-family': `AlibabaPuHuiTi, ${token.value.fontFamily}, sans-serif`,
    },
    // 左侧菜单样式
    'menu': {
      'background': `${token.value.colorBgLayout}80`,
      'width': '280px',
      'height': '100%',
      'display': 'flex',
      'flex-direction': 'column',
    },
    // 对话列表样式
    'conversations': {
      'padding': '0 12px',
      'flex': 1,
      'overflow-y': 'auto',
    },
    // 聊天区域样式
    'chat': {
      'height': '100%',
      'width': '100%',
      'max-width': '700px',
      'margin': '0 auto',
      'box-sizing': 'border-box',
      'display': 'flex',
      'flex-direction': 'column',
      'padding': `${token.value.paddingLG}px`,
      'gap': '16px',
    },
    // 消息列表样式
    'messages': {
      flex: 1,
    },
    // 占位符样式
    'placeholder': {
      'padding-top': '32px',
      'text-align': 'left',
      'flex': 1,
    },
    // 输入框样式
    'sender': {
      'box-shadow': token.value.boxShadow,
    },
    // Logo样式
    'logo': {
      'display': 'flex',
      'height': '72px',
      'align-items': 'center',
      'justify-content': 'start',
      'padding': '0 24px',
      'box-sizing': 'border-box',
    },
    'logo-img': {
      width: '24px',
      height: '24px',
      display: 'inline-block',
    },
    'logo-span': {
      'display': 'inline-block',
      'margin': '0 8px',
      'font-weight': 'bold',
      'color': token.value.colorText,
      'font-size': '16px',
    },
    // 新建对话按钮样式
    'addBtn': {
      background: '#1677ff0f',
      border: '1px solid #1677ff34',
      width: 'calc(100% - 24px)',
      margin: '0 12px 24px 12px',
    },
  } as const
})

// 定义组件名称
defineOptions({name: 'PlaygroundIndependentSetup'})

// ==================== 工具函数 ====================
// 渲染带图标的标题
function renderTitle(icon: VNode, title: string) {
  return h(Space, {align: 'start'}, () => [icon, h('span', title)])
}

// ==================== 初始数据 ====================
// 默认对话列表
const defaultConversationsItems = [
  {
    key: '0',
    label: 'What is Ant Design X?',
  },
]

// 欢迎页面的提示词选项
const placeholderPromptsItems: PromptsProps['items'] = [
  {
    key: '1',
    label: renderTitle(h(FireOutlined, {style: {color: '#FF4D4F'}}), 'Hot Topics'),
    description: 'What are you interested in?',
    children: [
      {
        key: '1-1',
        description: `What's new in X?`,
      },
      {
        key: '1-2',
        description: `What's AGI?`,
      },
      {
        key: '1-3',
        description: `Where is the doc?`,
      },
    ],
  },
  {
    key: '2',
    label: renderTitle(h(ReadOutlined, {style: {color: '#1890FF'}}), 'Design Guide'),
    description: 'How to design a good product?',
    children: [
      {
        key: '2-1',
        icon: h(HeartOutlined),
        description: `Know the well`,
      },
      {
        key: '2-2',
        icon: h(SmileOutlined),
        description: `Set the AI role`,
      },
      {
        key: '2-3',
        icon: h(CommentOutlined),
        description: `Express the feeling`,
      },
    ],
  },
]

// 输入框下方的快捷提示词
const senderPromptsItems: PromptsProps['items'] = [
  {
    key: '1',
    description: 'Hot Topics',
    icon: h(FireOutlined, {style: {color: '#FF4D4F'}}),
  },
  {
    key: '2',
    description: 'Design Guide',
    icon: h(ReadOutlined, {style: {color: '#1890FF'}}),
  },
]

// ==================== 消息角色配置 ====================
// 定义AI和用户消息的显示样式
const roles: BubbleListProps['roles'] = {
  // AI消息样式配置
  ai: {
    placement: 'start', // 左对齐
    avatar: {icon: h(UserOutlined), style: {background: '#fde3cf'}},
    typing: {step: 5, interval: 20}, // 打字机效果配置
    styles: {
      content: {
        borderRadius: '16px', // 圆角
      },
    },
  },
  // 用户消息样式配置
  local: {
    placement: 'end', // 右对齐
    avatar: {icon: h(UserOutlined), style: {background: '#87d068'}},
    variant: 'shadow', // 阴影样式
  },
  // 新增文件样式
  // file: {
  //   placement: 'end',
  //   variant: 'borderless',
  //   messageRender: (items) => h(
  //       Flex,
  //       {vertical: true, gap: 'middle'},
  //       () => (items as any[]).map((item) => h(
  //           Attachments.FileCard,
  //           {key: item.uid, item}
  //       ))
  //       // () => (items as any[]).map((item) => h(Space, {
  //       //       key: item.uid,
  //       //       align: 'center',
  //       //       style: {
  //       //         padding: '8px 12px',
  //       //         background: '#f5f5f5',
  //       //         borderRadius: '6px',
  //       //         border: '1px solid #d9d9d9',
  //       //         width: '100%'
  //       //       }
  //       //     }, () => [
  //       //       h(FileOutlined, {
  //       //         style: {
  //       //           color: '#1890ff',
  //       //           fontSize: '16px'
  //       //         }
  //       //       }),
  //       //       h(Typography.Text, {
  //       //         style: {
  //       //           flex: 1,
  //       //           fontSize: '14px'
  //       //         }
  //       //       }, () => item.name),
  //       //       item.size && h(Typography.Text, {
  //       //         type: 'secondary',
  //       //         style: {
  //       //           fontSize: '12px'
  //       //         }
  //       //       }, () => `${(item.size / 1024).toFixed(2)} KB`)
  //       //     ]))
  //   ),
  // },
  file: {
    placement: 'end',
    variant: 'borderless',
    messageRender: (items) => h(
        Flex,
        {vertical: true, gap: 'middle'}, // 纵向排列文件卡片，保持间距
        () => (items as any[]).map((item) => {
          // 处理文件大小：兼容无size字段场景，统一格式为 KB/MB
          const formatFileSize = () => {
            if (!item.size) return '';
            const sizeInKB = item.size / 1024;
            // 大于1024KB时显示MB，保留2位小数
            return sizeInKB > 1024
                ? `${(sizeInKB / 1024).toFixed(2)} MB`
                : `${sizeInKB.toFixed(2)} KB`;
          };

          // 判断是否为图片文件（根据URL后缀简单判断，可根据实际需求扩展）
          const isImageFile = item.url && /\.(png|jpg|jpeg|gif|svg)$/i.test(item.url);

          return h(
              Space,
              {
                key: item.uid,
                align: 'center',
                style: {
                  padding: '12px 16px', // 适当扩大内边距，提升质感
                  background: '#ffffff',
                  borderRadius: '8px',
                  border: '1px solid #e8e8e8',
                  width: '100%',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.04)', // 新增轻微阴影，贴近卡片效果
                  cursor: 'pointer', //  hover时显示指针，模拟卡片交互
                  transition: 'border-color 0.2s', // 边框颜色过渡，提升交互感
                },
                onMouseEnter: (e: any) => {
                  e.currentTarget.style.borderColor = '#1890ff'; // hover时边框变主色
                },
                onMouseLeave: (e: any) => {
                  e.currentTarget.style.borderColor = '#e8e8e8'; // 离开时恢复边框色
                },
              },
              () => [
                // 1. 媒体预览区：图片显示缩略图，非图片显示文件图标
                isImageFile
                    ? h(
                        'img',
                        {
                          style: {
                            width: '40px',
                            height: '40px',
                            objectFit: 'cover', // 保持图片比例，避免拉伸
                            borderRadius: '4px',
                            border: '1px solid #f0f0f0',
                          },
                          src: item.url,
                          alt: item.name, // 图片加载失败时显示文件名
                          title: item.name, // 鼠标悬浮显示完整文件名
                        }
                    )
                    : h(
                        FileOutlined,
                        {
                          style: {
                            color: '#1890ff',
                            fontSize: '20px', // 非图片文件图标放大，提升视觉权重
                          },
                        }
                    ),

                // 2. 文件信息区：文件名 + 文件大小（纵向排列，更清晰）
                h(
                    Flex,
                    {vertical: true, gap: 'small', style: {flex: 1, margin: '0 12px'}},
                    () => [
                      // 文件名：超出部分省略号显示，避免换行
                      h(
                          Typography.Text,
                          {
                            style: {
                              fontSize: '14px',
                              color: '#333333',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              width: '100%',
                            },
                            title: item.name, // 文件名过长时，hover显示完整名称
                          },
                          () => item.name
                      ),

                      // 文件大小：灰色小字显示
                      h(
                          Typography.Text,
                          {
                            type: 'secondary',
                            style: {
                              fontSize: '12px',
                              color: '#999999',
                            },
                          },
                          () => formatFileSize()
                      ),
                    ]
                ),

                // 3. 辅助操作区（可选）：可新增下载/预览按钮，贴近FileCard功能
                item.url && h(
                    Button,
                    {
                      size: 'small',
                      type: 'text',
                      icon: h(DownloadOutlined),
                      style: {color: '#1890ff'},
                      onClick: () => {
                        // 下载逻辑：创建临时a标签触发下载
                        const link = document.createElement('a');
                        link.href = item.url;
                        link.download = item.name; // 指定下载文件名
                        link.click();
                      },
                    },
                    '下载'
                ),
              ]
          );
        })
    ),
  },
}

// ==================== 响应式状态 ====================
// 文件上传区域是否展开
const headerOpen = ref(false)
// 输入框内容
const content = ref('')
// 对话列表数据
const conversationsItems = ref(defaultConversationsItems)
// 当前激活的对话key
const activeKey = ref(defaultConversationsItems[0].key)
// 已上传的文件列表
const attachedFiles = ref<AttachmentsProps['items']>([])
// AI请求加载状态
const agentRequestLoading = ref(false)

// ==================== 流式响应相关状态 ====================
// 当前流式响应的消息ID
const currentStreamingMessageId = ref<string | null>(null)
// 流式响应的累积内容
const streamingContent = ref('')
// 是否正在接收流式数据
const isStreaming = ref(false)

// ==================== 自定义消息管理 ====================
// 自定义消息列表，替代useXChat的消息管理
const customMessages = ref<Array<{
  id: string
  message: string
  status: 'local' | 'ai' | 'loading' | 'file'
  isStreaming?: boolean
}>>([])

// ==================== 流式请求函数 ====================
// 发送流式请求到后端
async function sendStreamRequest(message: string) {
  try {
    let filesPath : string[] = [];
    // 创建附件消息
    const attachmentMessageId = `attachment-${Date.now()}`
    customMessages.value.push({
      id: attachmentMessageId,
      message: attachedFiles.value,
      status: 'file',
    })
    console.log("attachedFiles", attachedFiles.value)
    // 创建用户消息
    const userMessageId = `user-${Date.now()}`
    customMessages.value.push({
      id: userMessageId,
      message,
      status: 'local'
    })
    // 创建AI消息占位符
    const aiMessageId = `ai-${Date.now()}`
    currentStreamingMessageId.value = aiMessageId
    streamingContent.value = ''
    isStreaming.value = true

    customMessages.value.push({
      id: aiMessageId,
      message: '',
      status: 'loading',
      isStreaming: true
    })

    const chatRequestBody = {
      message: message,
      userId: "user-01",
      sessionId: "session-01",
    }

    // 发送POST请求到后端流式接口
    const response = await fetch('http://localhost:8080/api/ai/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/plain',
      },
      body: JSON.stringify(chatRequestBody)
    })
    console.log('response', response)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // 获取响应流
    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('No response body reader available')
    }

    const decoder = new TextDecoder()

    // 读取流式数据
    while (true) {
      const {done, value} = await reader.read()

      if (done) {
        break
      }

      // 解码接收到的数据
      const chunk = decoder.decode(value, {stream: true})

      // 累积流式内容
      streamingContent.value += chunk

      // 更新消息内容
      const messageIndex = customMessages.value.findIndex(msg => msg.id === aiMessageId)
      if (messageIndex !== -1) {
        customMessages.value[messageIndex].message = streamingContent.value
        customMessages.value[messageIndex].status = 'ai'
      }
    }

  } catch (error) {
    console.error('Stream request failed:', error)

    // 处理错误，更新消息状态
    if (currentStreamingMessageId.value) {
      const messageIndex = customMessages.value.findIndex(msg => msg.id === currentStreamingMessageId.value)
      if (messageIndex !== -1) {
        customMessages.value[messageIndex].message = '抱歉，请求失败，请重试。'
        customMessages.value[messageIndex].status = 'ai'
        customMessages.value[messageIndex].isStreaming = false
      }
    }
  } finally {
    // 清理流式状态
    isStreaming.value = false
    currentStreamingMessageId.value = null
    streamingContent.value = ''
    // 清空已上传的文件
    attachedFiles.value = [];
    // 关闭文件上传区域
    headerOpen.value = false
  }
}

// ==================== 事件处理函数 ====================
// 处理消息提交
async function onSubmit(nextContent: string) {
  if (!nextContent || isStreaming.value) {
    return
  }

  console.log('onSubmit', nextContent)

  // 发送流式请求
  await sendStreamRequest(nextContent)

  // 清空输入框
  content.value = ''
}

// 处理提示词点击
const onPromptsItemClick: PromptsProps['onItemClick'] = async (info) => {
  const promptText = info.data.description as string
  if (promptText && !isStreaming.value) {
    await sendStreamRequest(promptText)
  }
}

// 添加新对话
function onAddConversation() {
  conversationsItems.value = [
    ...conversationsItems.value,
    {
      key: `${conversationsItems.value.length}`,
      label: `New Conversation ${conversationsItems.value.length}`,
    },
  ]
  activeKey.value = `${conversationsItems.value.length}`
  console.log("conversationsItems", conversationsItems.value)
}

// 处理对话切换
const onConversationClick: ConversationsProps['onActiveChange'] = (key) => {
  activeKey.value = key
  // 切换对话时清空消息列表
  customMessages.value = []
}

// 处理文件变化
const handleFileChange: AttachmentsProps['onChange'] = (info: UploadChangeParam<any>) => {
  console.log("handleFileChange triggered", info.file.status, info.file)
  console.log("info.file 的所有属性:", Object.keys(info.file))
  console.log("info.file 是否是 File 对象:", info.file instanceof File)
  console.log("info.file 的类型:", typeof info.file)

  // 只在文件添加时处理上传，避免重复调用
  if (info.file.status === 'uploading' || info.file.status === 'done') {
    // 如果文件已经有URL，说明已经上传过了，直接更新文件列表
    if (info.file.url) {
      attachedFiles.value = info.fileList
      return
    }

    // 尝试多种方式获取 File 对象
    let fileToUpload: File | null = null

    // 方式1: 检查 originFileObj 属性
    if (info.file.originFileObj && info.file.originFileObj instanceof File) {
      fileToUpload = info.file.originFileObj
      console.log("使用 originFileObj:", fileToUpload)
    }
    // 方式2: 检查 info.file 本身是否是 File 对象
    else if (info.file instanceof File) {
      fileToUpload = info.file
      console.log("使用 info.file 本身:", fileToUpload)
    }
    // 方式3: 检查其他可能的属性
    else if ((info.file as any).file && (info.file as any).file instanceof File) {
      fileToUpload = (info.file as any).file
      console.log("使用 file 属性:", fileToUpload)
    }
    // 方式4: 检查 raw 属性
    else if ((info.file as any).raw && (info.file as any).raw instanceof File) {
      fileToUpload = (info.file as any).raw
      console.log("使用 raw 属性:", fileToUpload)
    }

    if (fileToUpload) {
      const formData = new FormData()
      formData.append('file', fileToUpload)
      console.log("上传文件:", fileToUpload)

      fetch('http://localhost:8080/api/ai/upload', {
        method: 'POST',
        body: formData,
      }).then(response => {
        return response.text()
      }).then(data => {
        // 更新文件的URL
        info.file.url = data
        info.file.status = 'done'
        console.log("文件上传成功，URL:", data)
        // 更新文件列表
        attachedFiles.value = info.fileList
      }).catch(error => {
        console.error("文件上传失败:", error)
        info.file.status = 'error'
        // 即使上传失败也要更新文件列表以显示错误状态
        attachedFiles.value = info.fileList
      })
    } else {
      console.warn("无法找到有效的 File 对象:", info.file)
      console.warn("info.file 的完整内容:", JSON.stringify(info.file, null, 2))
    }
  } else {
    // 对于其他状态变化（如删除），直接更新文件列表
    attachedFiles.value = info.fileList
  }

  console.log("当前文件列表:", attachedFiles.value)
}

// ==================== 计算属性 ====================
// 欢迎页面节点 - 当没有消息时显示
const placeholderNode = computed(() => h(
    Space,
    {direction: "vertical", size: 16, style: styles.value.placeholder},
    () => [
      // 欢迎组件
      h(
          Welcome,
          {
            variant: "borderless",
            icon: "https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp",
            title: "Hello, I'm Ant Design X",
            description: "Base on Ant Design, AGI product interface solution, create a better intelligent vision~",
            extra: h(Space, {}, () => [h(Button, {icon: h(ShareAltOutlined)}), h(Button, {icon: h(EllipsisOutlined)})]),
          }
      ),
      // 提示词组件
      h(
          Prompts,
          {
            title: "Do you want?",
            items: placeholderPromptsItems,
            styles: {
              list: {
                width: '100%',
              },
              item: {
                flex: 1,
              },
            },
            onItemClick: onPromptsItemClick,
          }
      )
    ]
))

// 消息列表数据 - 根据当前状态动态计算
const items = computed<BubbleListProps['items']>(() => {
  // 如果没有消息，显示欢迎页面
  if (customMessages.value.length === 0) {
    return [{content: placeholderNode, variant: 'borderless'}]
  }

  // 将自定义消息数据转换为气泡组件需要的格式
  return customMessages.value.map(({id, message, status, isStreaming}) => ({
    key: id,
    loading: status === 'loading', // 加载状态
    role: status, // 角色判断
    content: status === 'ai' ? renderMarkdown(message) : message, // AI消息使用Markdown渲染
  }))
})

// ==================== 监听器 ====================
// 监听对话切换，清空消息列表
watch(activeKey, () => {
  customMessages.value = []
}, {immediate: true})
</script>

<style scoped>
/* Markdown样式优化 */
:deep(.markdown-content) {
  line-height: 1.6;
  word-wrap: break-word;
}

/* 代码块样式 */
:deep(.markdown-content pre) {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  margin: 8px 0;
}

:deep(.markdown-content code) {
  background-color: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
}

/* 引用块样式 */
:deep(.markdown-content blockquote) {
  border-left: 4px solid #dfe2e5;
  padding-left: 16px;
  margin: 8px 0;
  color: #6a737d;
}

/* 列表样式 */
:deep(.markdown-content ul),
:deep(.markdown-content ol) {
  padding-left: 20px;
  margin: 8px 0;
}

/* 链接样式 */
:deep(.markdown-content a) {
  color: #0366d6;
  text-decoration: none;
}

:deep(.markdown-content a:hover) {
  text-decoration: underline;
}

/* 表格样式 */
:deep(.markdown-content table) {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

:deep(.markdown-content th),
:deep(.markdown-content td) {
  border: 1px solid #dfe2e5;
  padding: 8px 12px;
  text-align: left;
}

:deep(.markdown-content th) {
  background-color: #f6f8fa;
  font-weight: 600;
}
</style>

